/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M15 5h6", key: "1pr8yx" }],
  ["path", { d: "M15 12h6", key: "upa0zy" }],
  ["path", { d: "M3 19h18", key: "awlh7x" }],
  ["path", { d: "m3 12 3.553-7.724a.5.5 0 0 1 .894 0L11 12", key: "6lvno8" }],
  ["path", { d: "M3.92 10h6.16", key: "1tl8ex" }]
];
const TextInitial = createLucideIcon("text-initial", __iconNode);

export { __iconNode, TextInitial as default };
//# sourceMappingURL=text-initial.js.map
