'use client';

import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import Image from 'next/image';

const About = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="about" className="py-20 bg-gradient-to-b from-black to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
        >
          {/* Image Section */}
          <motion.div variants={itemVariants} className="relative">
            <div className="relative w-full max-w-md mx-auto lg:max-w-none">
              <motion.div
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
                className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-800 to-gray-900 p-8"
              >
                {/* Placeholder for profile image */}
                <div className="w-full h-80 bg-gradient-to-br from-gray-700 to-gray-800 rounded-xl flex items-center justify-center">
                  <div className="text-6xl text-gray-500">👤</div>
                </div>
                
                {/* Decorative elements */}
                <div className="absolute top-4 right-4 w-20 h-20 bg-white/5 rounded-full blur-xl" />
                <div className="absolute bottom-4 left-4 w-16 h-16 bg-white/5 rounded-full blur-xl" />
              </motion.div>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div variants={itemVariants} className="space-y-6">
            <div>
              <motion.h2 
                variants={itemVariants}
                className="text-4xl sm:text-5xl font-bold mb-4"
              >
                About Me
              </motion.h2>
              <motion.div 
                variants={itemVariants}
                className="w-20 h-1 bg-white rounded-full mb-8"
              />
            </div>

            <motion.p 
              variants={itemVariants}
              className="text-lg text-gray-300 leading-relaxed"
            >
              I'm a passionate developer with a love for creating beautiful, functional, and 
              user-centered digital experiences. With expertise in modern web technologies, 
              I bring ideas to life through clean code and thoughtful design.
            </motion.p>

            <motion.p 
              variants={itemVariants}
              className="text-lg text-gray-300 leading-relaxed"
            >
              My journey in development started several years ago, and I've since worked on 
              various projects ranging from small business websites to large-scale applications. 
              I believe in continuous learning and staying up-to-date with the latest technologies.
            </motion.p>

            <motion.div 
              variants={itemVariants}
              className="grid grid-cols-2 gap-6 pt-6"
            >
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-white">Experience</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Frontend</span>
                    <span className="text-white">3+ years</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Backend</span>
                    <span className="text-white">2+ years</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Design</span>
                    <span className="text-white">2+ years</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-white">Focus Areas</h3>
                <div className="space-y-2 text-gray-300">
                  <div>• Web Development</div>
                  <div>• UI/UX Design</div>
                  <div>• Mobile Apps</div>
                  <div>• Performance</div>
                </div>
              </div>
            </motion.div>

            <motion.div 
              variants={itemVariants}
              className="pt-6"
            >
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}
                className="px-6 py-3 border border-white text-white hover:bg-white hover:text-black transition-all duration-300 rounded-full"
              >
                View My Work
              </motion.button>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
