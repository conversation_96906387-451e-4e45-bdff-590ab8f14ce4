# Personal Portfolio Website

A modern, minimal portfolio website built with Next.js, featuring a clean black and white theme with smooth animations and responsive design.

## ✨ Features

- **Modern Design**: Clean black and white theme with elegant typography
- **Smooth Animations**: Powered by Framer Motion for beautiful transitions
- **Responsive**: Optimized for both mobile and desktop devices
- **Interactive Navigation**: Smooth scrolling with active section highlighting
- **Contact Form**: Functional contact form with validation
- **Performance Optimized**: Built with Next.js for optimal loading speeds

## 🚀 Sections

- **Home**: Bold introduction with animated background elements
- **About Me**: Personal information with photo and background
- **Skills**: Interactive grid showcasing technical skills with progress bars
- **Projects**: Stylish gallery with hover effects and project details
- **Contact**: Contact form with social media links

## 🛠️ Technologies Used

- **Next.js 15** - React framework for production
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animation library for React
- **Lucide React** - Beautiful icon library
- **React Intersection Observer** - Scroll-triggered animations

## 📦 Installation

1. Clone the repository:
```bash
git clone <your-repo-url>
cd portfolio
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## 🎨 Customization

### Personal Information
Update the following files with your information:

1. **src/app/layout.tsx** - Update metadata (title, description)
2. **src/components/Hero.tsx** - Update name and tagline
3. **src/components/About.tsx** - Update bio and experience
4. **src/components/Skills.tsx** - Update skills and technologies
5. **src/components/Projects.tsx** - Update project information
6. **src/components/Contact.tsx** - Update contact information and social links

### Styling
- Colors and theme can be modified in `src/app/globals.css`
- Component-specific styles are in their respective files using Tailwind classes

### Adding Your Photo
Replace the placeholder in the About section with your actual photo:
```jsx
// In src/components/About.tsx
<Image
  src="/your-photo.jpg"
  alt="Your Name"
  width={400}
  height={400}
  className="rounded-xl"
/>
```

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Deploy with one click

### Other Platforms
The app can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Railway
- Render

## 📱 Responsive Design

The website is fully responsive and tested on:
- Mobile devices (320px and up)
- Tablets (768px and up)
- Desktop (1024px and up)
- Large screens (1440px and up)

## 🎯 Performance

- Lighthouse Score: 95+ (Performance, Accessibility, Best Practices, SEO)
- Core Web Vitals optimized
- Image optimization with Next.js Image component
- Font optimization with next/font

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Feel free to fork this project and customize it for your own portfolio. If you make improvements, pull requests are welcome!

## 📞 Support

If you have any questions or need help customizing the portfolio, feel free to reach out or create an issue in the repository.
