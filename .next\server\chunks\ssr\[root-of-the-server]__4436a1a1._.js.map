{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/Potfolio/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\n\nconst Navigation = () => {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [activeSection, setActiveSection] = useState('home');\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n      \n      // Update active section based on scroll position\n      const sections = ['home', 'about', 'skills', 'projects', 'contact'];\n      const scrollPosition = window.scrollY + 100;\n      \n      for (const section of sections) {\n        const element = document.getElementById(section);\n        if (element) {\n          const { offsetTop, offsetHeight } = element;\n          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {\n            setActiveSection(section);\n            break;\n          }\n        }\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  const navItems = [\n    { id: 'home', label: 'Home' },\n    { id: 'about', label: 'About' },\n    { id: 'skills', label: 'Skills' },\n    { id: 'projects', label: 'Projects' },\n    { id: 'contact', label: 'Contact' },\n  ];\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled \n          ? 'bg-black/90 backdrop-blur-md border-b border-gray-800' \n          : 'bg-transparent'\n      }`}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"text-xl font-bold cursor-pointer\"\n            onClick={() => scrollToSection('home')}\n          >\n            Your Name\n          </motion.div>\n          \n          <div className=\"hidden md:flex space-x-8\">\n            {navItems.map((item) => (\n              <motion.button\n                key={item.id}\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => scrollToSection(item.id)}\n                className={`relative px-3 py-2 text-sm font-medium transition-colors ${\n                  activeSection === item.id \n                    ? 'text-white' \n                    : 'text-gray-400 hover:text-white'\n                }`}\n              >\n                {item.label}\n                {activeSection === item.id && (\n                  <motion.div\n                    layoutId=\"activeSection\"\n                    className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-white\"\n                    initial={false}\n                    transition={{ type: \"spring\", stiffness: 380, damping: 30 }}\n                  />\n                )}\n              </motion.button>\n            ))}\n          </div>\n          \n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <motion.button\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"text-gray-400 hover:text-white\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d={isMobileMenuOpen ? \"M6 18L18 6M6 6l12 12\" : \"M4 6h16M4 12h16M4 18h16\"}\n                />\n              </svg>\n            </motion.button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      <motion.div\n        initial={{ opacity: 0, height: 0 }}\n        animate={{\n          opacity: isMobileMenuOpen ? 1 : 0,\n          height: isMobileMenuOpen ? 'auto' : 0\n        }}\n        transition={{ duration: 0.3 }}\n        className=\"md:hidden bg-black/95 backdrop-blur-md border-b border-gray-800 overflow-hidden\"\n      >\n        <div className=\"px-4 py-4 space-y-2\">\n          {navItems.map((item) => (\n            <motion.button\n              key={item.id}\n              whileHover={{ x: 10 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => {\n                scrollToSection(item.id);\n                setIsMobileMenuOpen(false);\n              }}\n              className={`block w-full text-left px-3 py-2 text-base font-medium transition-colors ${\n                activeSection === item.id\n                  ? 'text-white bg-gray-800'\n                  : 'text-gray-400 hover:text-white hover:bg-gray-800'\n              } rounded-lg`}\n            >\n              {item.label}\n            </motion.button>\n          ))}\n        </div>\n      </motion.div>\n    </motion.nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,aAAa;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,iNAAQ,EAAC;IAEzD,IAAA,kNAAS,EAAC;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;YAE/B,iDAAiD;YACjD,MAAM,WAAW;gBAAC;gBAAQ;gBAAS;gBAAU;gBAAY;aAAU;YACnE,MAAM,iBAAiB,OAAO,OAAO,GAAG;YAExC,KAAK,MAAM,WAAW,SAAU;gBAC9B,MAAM,UAAU,SAAS,cAAc,CAAC;gBACxC,IAAI,SAAS;oBACX,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG;oBACpC,IAAI,kBAAkB,aAAa,iBAAiB,YAAY,cAAc;wBAC5E,iBAAiB;wBACjB;oBACF;gBACF;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,MAAM,WAAW;QACf;YAAE,IAAI;YAAQ,OAAO;QAAO;QAC5B;YAAE,IAAI;YAAS,OAAO;QAAQ;QAC9B;YAAE,IAAI;YAAU,OAAO;QAAS;QAChC;YAAE,IAAI;YAAY,OAAO;QAAW;QACpC;YAAE,IAAI;YAAW,OAAO;QAAU;KACnC;IAED,qBACE,8OAAC,oMAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,WAAW,CAAC,4DAA4D,EACtE,aACI,0DACA,kBACJ;;0BAEF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;4BACV,SAAS,IAAM,gBAAgB;sCAChC;;;;;;sCAID,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,oMAAM,CAAC,MAAM;oCAEZ,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,gBAAgB,KAAK,EAAE;oCACtC,WAAW,CAAC,yDAAyD,EACnE,kBAAkB,KAAK,EAAE,GACrB,eACA,kCACJ;;wCAED,KAAK,KAAK;wCACV,kBAAkB,KAAK,EAAE,kBACxB,8OAAC,oMAAM,CAAC,GAAG;4CACT,UAAS;4CACT,WAAU;4CACV,SAAS;4CACT,YAAY;gDAAE,MAAM;gDAAU,WAAW;gDAAK,SAAS;4CAAG;;;;;;;mCAhBzD,KAAK,EAAE;;;;;;;;;;sCAwBlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oMAAM,CAAC,MAAM;gCACZ,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS,IAAM,oBAAoB,CAAC;gCACpC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC9D,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAG,mBAAmB,yBAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3D,8OAAC,oMAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBACjC,SAAS;oBACP,SAAS,mBAAmB,IAAI;oBAChC,QAAQ,mBAAmB,SAAS;gBACtC;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,oMAAM,CAAC,MAAM;4BAEZ,YAAY;gCAAE,GAAG;4BAAG;4BACpB,UAAU;gCAAE,OAAO;4BAAK;4BACxB,SAAS;gCACP,gBAAgB,KAAK,EAAE;gCACvB,oBAAoB;4BACtB;4BACA,WAAW,CAAC,yEAAyE,EACnF,kBAAkB,KAAK,EAAE,GACrB,2BACA,mDACL,WAAW,CAAC;sCAEZ,KAAK,KAAK;2BAbN,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AAoB1B;uCAEe", "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/Potfolio/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ChevronDown } from 'lucide-react';\n\nconst Hero = () => {\n  const scrollToAbout = () => {\n    const aboutSection = document.getElementById('about');\n    if (aboutSection) {\n      aboutSection.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section id=\"home\" className=\"min-h-screen flex items-center justify-center relative overflow-hidden\">\n      {/* Background gradient */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black opacity-50\" />\n      \n      {/* Animated background elements */}\n      <div className=\"absolute inset-0\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-1 h-1 bg-white rounded-full opacity-20\"\n            initial={{\n              x: Math.random() * 1200,\n              y: Math.random() * 800\n            }}\n            animate={{\n              y: [null, -100, 1000],\n            }}\n            transition={{\n              duration: Math.random() * 10 + 10,\n              repeat: Infinity,\n              ease: \"linear\",\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n        >\n          <motion.h1\n            className=\"text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n          >\n            <span className=\"block\">Hello, I'm</span>\n            <motion.span \n              className=\"block bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent\"\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n            >\n              Your Name\n            </motion.span>\n          </motion.h1>\n          \n          <motion.p\n            className=\"text-lg sm:text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto px-4\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n          >\n            A passionate developer creating beautiful, functional, and user-centered digital experiences.\n          </motion.p>\n          \n          <motion.div \n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.0 }}\n          >\n            <motion.button\n              whileHover={{ scale: 1.05, boxShadow: \"0 10px 30px rgba(255,255,255,0.1)\" }}\n              whileTap={{ scale: 0.95 }}\n              onClick={scrollToAbout}\n              className=\"px-8 py-3 bg-white text-black font-semibold rounded-full hover:bg-gray-200 transition-all duration-300\"\n            >\n              Learn More\n            </motion.button>\n            \n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}\n              className=\"px-8 py-3 border-2 border-white text-white font-semibold rounded-full hover:bg-white hover:text-black transition-all duration-300\"\n            >\n              Get In Touch\n            </motion.button>\n          </motion.div>\n        </motion.div>\n      </div>\n      \n      {/* Scroll indicator */}\n      <motion.div \n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 cursor-pointer\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, delay: 1.2 }}\n        onClick={scrollToAbout}\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"flex flex-col items-center text-gray-400 hover:text-white transition-colors\"\n        >\n          <span className=\"text-sm mb-2\">Scroll Down</span>\n          <ChevronDown size={24} />\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,OAAO;IACX,MAAM,gBAAgB;QACpB,MAAM,eAAe,SAAS,cAAc,CAAC;QAC7C,IAAI,cAAc;YAChB,aAAa,cAAc,CAAC;gBAAE,UAAU;YAAS;QACnD;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;;0BAE3B,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,oMAAM,CAAC,GAAG;wBAET,WAAU;wBACV,SAAS;4BACP,GAAG,KAAK,MAAM,KAAK;4BACnB,GAAG,KAAK,MAAM,KAAK;wBACrB;wBACA,SAAS;4BACP,GAAG;gCAAC;gCAAM,CAAC;gCAAK;6BAAK;wBACvB;wBACA,YAAY;4BACV,UAAU,KAAK,MAAM,KAAK,KAAK;4BAC/B,QAAQ;4BACR,MAAM;wBACR;uBAbK;;;;;;;;;;0BAkBX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;;sCAExC,8OAAC,oMAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,8OAAC;oCAAK,WAAU;8CAAQ;;;;;;8CACxB,8OAAC,oMAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CACzC;;;;;;;;;;;;sCAKH,8OAAC,oMAAM,CAAC,CAAC;4BACP,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCACzC;;;;;;sCAID,8OAAC,oMAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,8OAAC,oMAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;wCAAM,WAAW;oCAAoC;oCAC1E,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS;oCACT,WAAU;8CACX;;;;;;8CAID,8OAAC,oMAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,SAAS,cAAc,CAAC,YAAY,eAAe;4CAAE,UAAU;wCAAS;oCACvF,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC,oMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,SAAS;0BAET,cAAA,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;;sCAEV,8OAAC;4BAAK,WAAU;sCAAe;;;;;;sCAC/B,8OAAC,mOAAW;4BAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;AAK7B;uCAEe", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/Potfolio/src/components/About.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport Image from 'next/image';\n\nconst About = () => {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.3,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.8,\n        ease: \"easeOut\",\n      },\n    },\n  };\n\n  return (\n    <section id=\"about\" className=\"py-20 bg-gradient-to-b from-black to-gray-900\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          ref={ref}\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={inView ? \"visible\" : \"hidden\"}\n          className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\"\n        >\n          {/* Image Section */}\n          <motion.div variants={itemVariants} className=\"relative\">\n            <div className=\"relative w-full max-w-md mx-auto lg:max-w-none\">\n              <motion.div\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n                className=\"relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-800 to-gray-900 p-8\"\n              >\n                {/* Placeholder for profile image */}\n                <div className=\"w-full h-80 bg-gradient-to-br from-gray-700 to-gray-800 rounded-xl flex items-center justify-center\">\n                  <div className=\"text-6xl text-gray-500\">👤</div>\n                </div>\n                \n                {/* Decorative elements */}\n                <div className=\"absolute top-4 right-4 w-20 h-20 bg-white/5 rounded-full blur-xl\" />\n                <div className=\"absolute bottom-4 left-4 w-16 h-16 bg-white/5 rounded-full blur-xl\" />\n              </motion.div>\n            </div>\n          </motion.div>\n\n          {/* Content Section */}\n          <motion.div variants={itemVariants} className=\"space-y-6\">\n            <div>\n              <motion.h2 \n                variants={itemVariants}\n                className=\"text-4xl sm:text-5xl font-bold mb-4\"\n              >\n                About Me\n              </motion.h2>\n              <motion.div \n                variants={itemVariants}\n                className=\"w-20 h-1 bg-white rounded-full mb-8\"\n              />\n            </div>\n\n            <motion.p \n              variants={itemVariants}\n              className=\"text-lg text-gray-300 leading-relaxed\"\n            >\n              I'm a passionate developer with a love for creating beautiful, functional, and \n              user-centered digital experiences. With expertise in modern web technologies, \n              I bring ideas to life through clean code and thoughtful design.\n            </motion.p>\n\n            <motion.p \n              variants={itemVariants}\n              className=\"text-lg text-gray-300 leading-relaxed\"\n            >\n              My journey in development started several years ago, and I've since worked on \n              various projects ranging from small business websites to large-scale applications. \n              I believe in continuous learning and staying up-to-date with the latest technologies.\n            </motion.p>\n\n            <motion.div \n              variants={itemVariants}\n              className=\"grid grid-cols-2 gap-6 pt-6\"\n            >\n              <div className=\"space-y-4\">\n                <h3 className=\"text-xl font-semibold text-white\">Experience</h3>\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-300\">Frontend</span>\n                    <span className=\"text-white\">3+ years</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-300\">Backend</span>\n                    <span className=\"text-white\">2+ years</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-300\">Design</span>\n                    <span className=\"text-white\">2+ years</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"space-y-4\">\n                <h3 className=\"text-xl font-semibold text-white\">Focus Areas</h3>\n                <div className=\"space-y-2 text-gray-300\">\n                  <div>• Web Development</div>\n                  <div>• UI/UX Design</div>\n                  <div>• Mobile Apps</div>\n                  <div>• Performance</div>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.div \n              variants={itemVariants}\n              className=\"pt-6\"\n            >\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}\n                className=\"px-6 py-3 border border-white text-white hover:bg-white hover:text-black transition-all duration-300 rounded-full\"\n              >\n                View My Work\n              </motion.button>\n            </motion.div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default About;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAMA,MAAM,QAAQ;IACZ,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,gLAAS,EAAC;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,oMAAM,CAAC,GAAG;gBACT,KAAK;gBACL,UAAU;gBACV,SAAQ;gBACR,SAAS,SAAS,YAAY;gBAC9B,WAAU;;kCAGV,8OAAC,oMAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;kCAC5C,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oMAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAGV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDAAyB;;;;;;;;;;;kDAI1C,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMrB,8OAAC,oMAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC;;kDACC,8OAAC,oMAAM,CAAC,EAAE;wCACR,UAAU;wCACV,WAAU;kDACX;;;;;;kDAGD,8OAAC,oMAAM,CAAC,GAAG;wCACT,UAAU;wCACV,WAAU;;;;;;;;;;;;0CAId,8OAAC,oMAAM,CAAC,CAAC;gCACP,UAAU;gCACV,WAAU;0CACX;;;;;;0CAMD,8OAAC,oMAAM,CAAC,CAAC;gCACP,UAAU;gCACV,WAAU;0CACX;;;;;;0CAMD,8OAAC,oMAAM,CAAC,GAAG;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAa;;;;;;;;;;;;kEAE/B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAa;;;;;;;;;;;;kEAE/B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAa;;;;;;;;;;;;;;;;;;;;;;;;kDAKnC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAI;;;;;;kEACL,8OAAC;kEAAI;;;;;;kEACL,8OAAC;kEAAI;;;;;;kEACL,8OAAC;kEAAI;;;;;;;;;;;;;;;;;;;;;;;;0CAKX,8OAAC,oMAAM,CAAC,GAAG;gCACT,UAAU;gCACV,WAAU;0CAEV,cAAA,8OAAC,oMAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,SAAS,cAAc,CAAC,aAAa,eAAe;4CAAE,UAAU;wCAAS;oCACxF,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/Potfolio/src/components/Skills.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { \n  Code, \n  Palette, \n  Database, \n  Globe, \n  Smartphone, \n  Zap,\n  GitBranch,\n  Settings\n} from 'lucide-react';\n\nconst Skills = () => {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const skills = [\n    {\n      icon: Code,\n      title: 'Frontend Development',\n      description: 'React, Next.js, TypeScript, Tailwind CSS',\n      level: 90,\n    },\n    {\n      icon: Database,\n      title: 'Backend Development',\n      description: 'Node.js, Python, PostgreSQL, MongoDB',\n      level: 85,\n    },\n    {\n      icon: Palette,\n      title: 'UI/UX Design',\n      description: 'Figma, Adobe XD, Responsive Design',\n      level: 80,\n    },\n    {\n      icon: Globe,\n      title: 'Web Technologies',\n      description: 'HTML5, CSS3, JavaScript, REST APIs',\n      level: 95,\n    },\n    {\n      icon: Smartphone,\n      title: 'Mobile Development',\n      description: 'React Native, Flutter, Progressive Web Apps',\n      level: 75,\n    },\n    {\n      icon: GitBranch,\n      title: 'Version Control',\n      description: 'Git, GitHub, GitLab, Collaborative Development',\n      level: 90,\n    },\n    {\n      icon: Zap,\n      title: 'Performance',\n      description: 'Optimization, SEO, Core Web Vitals',\n      level: 85,\n    },\n    {\n      icon: Settings,\n      title: 'DevOps',\n      description: 'Docker, AWS, CI/CD, Deployment',\n      level: 70,\n    },\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\",\n      },\n    },\n  };\n\n  return (\n    <section id=\"skills\" className=\"py-20 bg-black\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl sm:text-5xl font-bold mb-4\">Skills & Expertise</h2>\n          <div className=\"w-20 h-1 bg-white rounded-full mx-auto mb-6\" />\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            A comprehensive set of skills and technologies I use to bring ideas to life\n          </p>\n        </motion.div>\n\n        <motion.div\n          ref={ref}\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={inView ? \"visible\" : \"hidden\"}\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\"\n        >\n          {skills.map((skill, index) => (\n            <motion.div\n              key={index}\n              variants={itemVariants}\n              whileHover={{ \n                scale: 1.05, \n                boxShadow: \"0 20px 40px rgba(255,255,255,0.1)\" \n              }}\n              className=\"bg-gradient-to-br from-gray-900 to-gray-800 p-6 rounded-xl border border-gray-700 hover:border-gray-600 transition-all duration-300\"\n            >\n              <div className=\"flex flex-col items-center text-center space-y-4\">\n                <motion.div\n                  whileHover={{ rotate: 360 }}\n                  transition={{ duration: 0.6 }}\n                  className=\"p-3 bg-white/10 rounded-full\"\n                >\n                  <skill.icon size={32} className=\"text-white\" />\n                </motion.div>\n                \n                <h3 className=\"text-lg font-semibold text-white\">{skill.title}</h3>\n                \n                <p className=\"text-sm text-gray-400 leading-relaxed\">\n                  {skill.description}\n                </p>\n                \n                {/* Skill level bar */}\n                <div className=\"w-full\">\n                  <div className=\"flex justify-between text-xs text-gray-400 mb-1\">\n                    <span>Proficiency</span>\n                    <span>{skill.level}%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                    <motion.div\n                      initial={{ width: 0 }}\n                      animate={inView ? { width: `${skill.level}%` } : { width: 0 }}\n                      transition={{ duration: 1.5, delay: index * 0.1 }}\n                      className=\"bg-gradient-to-r from-white to-gray-300 h-2 rounded-full\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Additional skills section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.8, delay: 0.5 }}\n          className=\"mt-16 text-center\"\n        >\n          <h3 className=\"text-2xl font-semibold mb-6\">Technologies I Work With</h3>\n          <div className=\"flex flex-wrap justify-center gap-3\">\n            {[\n              'JavaScript', 'TypeScript', 'React', 'Next.js', 'Node.js', 'Python',\n              'PostgreSQL', 'MongoDB', 'Tailwind CSS', 'Framer Motion', 'Git',\n              'Docker', 'AWS', 'Figma', 'Adobe Creative Suite'\n            ].map((tech, index) => (\n              <motion.span\n                key={index}\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={inView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}\n                transition={{ duration: 0.5, delay: index * 0.05 }}\n                whileHover={{ scale: 1.1 }}\n                className=\"px-4 py-2 bg-gray-800 text-gray-300 rounded-full text-sm border border-gray-700 hover:border-gray-600 hover:text-white transition-all duration-300\"\n              >\n                {tech}\n              </motion.span>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Skills;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAeA,MAAM,SAAS;IACb,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,gLAAS,EAAC;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,SAAS;QACb;YACE,MAAM,0MAAI;YACV,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,sNAAQ;YACd,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,mNAAO;YACb,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,6MAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,4NAAU;YAChB,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,6NAAS;YACf,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,uMAAG;YACT,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,sNAAQ;YACd,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAS,WAAU;kBAC7B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC,oMAAM,CAAC,GAAG;oBACT,KAAK;oBACL,UAAU;oBACV,SAAQ;oBACR,SAAS,SAAS,YAAY;oBAC9B,WAAU;8BAET,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,oMAAM,CAAC,GAAG;4BAET,UAAU;4BACV,YAAY;gCACV,OAAO;gCACP,WAAW;4BACb;4BACA,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,QAAQ;wCAAI;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;kDAEV,cAAA,8OAAC,MAAM,IAAI;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;kDAAoC,MAAM,KAAK;;;;;;kDAE7D,8OAAC;wCAAE,WAAU;kDACV,MAAM,WAAW;;;;;;kDAIpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;;4DAAM,MAAM,KAAK;4DAAC;;;;;;;;;;;;;0DAErB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,OAAO;oDAAE;oDACpB,SAAS,SAAS;wDAAE,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;oDAAC,IAAI;wDAAE,OAAO;oDAAE;oDAC5D,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;oDAChD,WAAU;;;;;;;;;;;;;;;;;;;;;;;2BAlCb;;;;;;;;;;8BA4CX,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAA8B;;;;;;sCAC5C,8OAAC;4BAAI,WAAU;sCACZ;gCACC;gCAAc;gCAAc;gCAAS;gCAAW;gCAAW;gCAC3D;gCAAc;gCAAW;gCAAgB;gCAAiB;gCAC1D;gCAAU;gCAAO;gCAAS;6BAC3B,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,oMAAM,CAAC,IAAI;oCAEV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE,IAAI;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCACtE,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAK;oCACjD,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CAET;mCAPI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAerB;uCAEe", "debugId": null}}, {"offset": {"line": 1286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/Potfolio/src/components/Projects.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { ExternalLink, Github, ArrowRight } from 'lucide-react';\n\nconst Projects = () => {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const projects = [\n    {\n      title: 'E-Commerce Platform',\n      description: 'A full-stack e-commerce solution with modern design, secure payments, and admin dashboard.',\n      image: '/api/placeholder/600/400',\n      technologies: ['Next.js', 'TypeScript', 'Stripe', 'PostgreSQL'],\n      liveUrl: '#',\n      githubUrl: '#',\n      featured: true,\n    },\n    {\n      title: 'Task Management App',\n      description: 'Collaborative task management tool with real-time updates and team collaboration features.',\n      image: '/api/placeholder/600/400',\n      technologies: ['React', 'Node.js', 'Socket.io', 'MongoDB'],\n      liveUrl: '#',\n      githubUrl: '#',\n      featured: true,\n    },\n    {\n      title: 'Portfolio Website',\n      description: 'A responsive portfolio website showcasing creative design and smooth animations.',\n      image: '/api/placeholder/600/400',\n      technologies: ['Next.js', 'Framer Motion', 'Tailwind CSS'],\n      liveUrl: '#',\n      githubUrl: '#',\n      featured: false,\n    },\n    {\n      title: 'Weather Dashboard',\n      description: 'Real-time weather application with location-based forecasts and interactive maps.',\n      image: '/api/placeholder/600/400',\n      technologies: ['React', 'Weather API', 'Chart.js', 'CSS3'],\n      liveUrl: '#',\n      githubUrl: '#',\n      featured: false,\n    },\n    {\n      title: 'Social Media App',\n      description: 'A social platform with user authentication, posts, comments, and real-time messaging.',\n      image: '/api/placeholder/600/400',\n      technologies: ['React Native', 'Firebase', 'Redux', 'Node.js'],\n      liveUrl: '#',\n      githubUrl: '#',\n      featured: false,\n    },\n    {\n      title: 'Analytics Dashboard',\n      description: 'Business intelligence dashboard with data visualization and reporting capabilities.',\n      image: '/api/placeholder/600/400',\n      technologies: ['Vue.js', 'D3.js', 'Python', 'PostgreSQL'],\n      liveUrl: '#',\n      githubUrl: '#',\n      featured: false,\n    },\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.8,\n        ease: \"easeOut\",\n      },\n    },\n  };\n\n  return (\n    <section id=\"projects\" className=\"py-20 bg-gradient-to-b from-gray-900 to-black\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl sm:text-5xl font-bold mb-4\">Featured Projects</h2>\n          <div className=\"w-20 h-1 bg-white rounded-full mx-auto mb-6\" />\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            A showcase of my recent work and personal projects\n          </p>\n        </motion.div>\n\n        <motion.div\n          ref={ref}\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={inView ? \"visible\" : \"hidden\"}\n          className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12\"\n        >\n          {/* Featured Projects */}\n          {projects.filter(project => project.featured).map((project, index) => (\n            <motion.div\n              key={index}\n              variants={itemVariants}\n              whileHover={{ y: -10 }}\n              className=\"group relative bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl overflow-hidden border border-gray-700 hover:border-gray-600 transition-all duration-300\"\n            >\n              {/* Project Image */}\n              <div className=\"relative h-64 bg-gradient-to-br from-gray-700 to-gray-800 overflow-hidden\">\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-10\" />\n                <div className=\"w-full h-full flex items-center justify-center text-gray-500 text-6xl\">\n                  🖼️\n                </div>\n                \n                {/* Overlay on hover */}\n                <motion.div\n                  initial={{ opacity: 0 }}\n                  whileHover={{ opacity: 1 }}\n                  className=\"absolute inset-0 bg-black/70 z-20 flex items-center justify-center space-x-4\"\n                >\n                  <motion.a\n                    href={project.liveUrl}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"p-3 bg-white text-black rounded-full hover:bg-gray-200 transition-colors\"\n                  >\n                    <ExternalLink size={20} />\n                  </motion.a>\n                  <motion.a\n                    href={project.githubUrl}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"p-3 bg-white text-black rounded-full hover:bg-gray-200 transition-colors\"\n                  >\n                    <Github size={20} />\n                  </motion.a>\n                </motion.div>\n              </div>\n\n              {/* Project Content */}\n              <div className=\"p-6\">\n                <h3 className=\"text-xl font-semibold mb-3 group-hover:text-white transition-colors\">\n                  {project.title}\n                </h3>\n                <p className=\"text-gray-400 mb-4 leading-relaxed\">\n                  {project.description}\n                </p>\n                \n                {/* Technologies */}\n                <div className=\"flex flex-wrap gap-2 mb-4\">\n                  {project.technologies.map((tech, techIndex) => (\n                    <span\n                      key={techIndex}\n                      className=\"px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded-full\"\n                    >\n                      {tech}\n                    </span>\n                  ))}\n                </div>\n\n                {/* Links */}\n                <div className=\"flex items-center space-x-4\">\n                  <a\n                    href={project.liveUrl}\n                    className=\"flex items-center space-x-2 text-white hover:text-gray-300 transition-colors\"\n                  >\n                    <span>Live Demo</span>\n                    <ArrowRight size={16} />\n                  </a>\n                  <a\n                    href={project.githubUrl}\n                    className=\"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors\"\n                  >\n                    <Github size={16} />\n                    <span>Code</span>\n                  </a>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Other Projects Grid */}\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={inView ? \"visible\" : \"hidden\"}\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\"\n        >\n          {projects.filter(project => !project.featured).map((project, index) => (\n            <motion.div\n              key={index}\n              variants={itemVariants}\n              whileHover={{ y: -5, scale: 1.02 }}\n              className=\"group bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl overflow-hidden border border-gray-700 hover:border-gray-600 transition-all duration-300\"\n            >\n              {/* Project Image */}\n              <div className=\"relative h-48 bg-gradient-to-br from-gray-700 to-gray-800\">\n                <div className=\"w-full h-full flex items-center justify-center text-gray-500 text-4xl\">\n                  🖼️\n                </div>\n              </div>\n\n              {/* Project Content */}\n              <div className=\"p-4\">\n                <h3 className=\"text-lg font-semibold mb-2\">{project.title}</h3>\n                <p className=\"text-gray-400 text-sm mb-3 line-clamp-2\">\n                  {project.description}\n                </p>\n                \n                {/* Technologies */}\n                <div className=\"flex flex-wrap gap-1 mb-3\">\n                  {project.technologies.slice(0, 3).map((tech, techIndex) => (\n                    <span\n                      key={techIndex}\n                      className=\"px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded\"\n                    >\n                      {tech}\n                    </span>\n                  ))}\n                </div>\n\n                {/* Links */}\n                <div className=\"flex items-center justify-between\">\n                  <a\n                    href={project.liveUrl}\n                    className=\"text-white hover:text-gray-300 transition-colors text-sm\"\n                  >\n                    View Project\n                  </a>\n                  <a\n                    href={project.githubUrl}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    <Github size={16} />\n                  </a>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* View More Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.8, delay: 0.5 }}\n          className=\"text-center mt-12\"\n        >\n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"px-8 py-3 border border-white text-white hover:bg-white hover:text-black transition-all duration-300 rounded-full\"\n          >\n            View All Projects\n          </motion.button>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Projects;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,WAAW;IACf,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,gLAAS,EAAC;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,cAAc;gBAAC;gBAAW;gBAAc;gBAAU;aAAa;YAC/D,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,cAAc;gBAAC;gBAAS;gBAAW;gBAAa;aAAU;YAC1D,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,cAAc;gBAAC;gBAAW;gBAAiB;aAAe;YAC1D,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,cAAc;gBAAC;gBAAS;gBAAe;gBAAY;aAAO;YAC1D,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,cAAc;gBAAC;gBAAgB;gBAAY;gBAAS;aAAU;YAC9D,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,OAAO;YACP,cAAc;gBAAC;gBAAU;gBAAS;gBAAU;aAAa;YACzD,SAAS;YACT,WAAW;YACX,UAAU;QACZ;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC,oMAAM,CAAC,GAAG;oBACT,KAAK;oBACL,UAAU;oBACV,SAAQ;oBACR,SAAS,SAAS,YAAY;oBAC9B,WAAU;8BAGT,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,EAAE,GAAG,CAAC,CAAC,SAAS,sBAC1D,8OAAC,oMAAM,CAAC,GAAG;4BAET,UAAU;4BACV,YAAY;gCAAE,GAAG,CAAC;4BAAG;4BACrB,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;sDAAwE;;;;;;sDAKvF,8OAAC,oMAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;4CAAE;4CACtB,YAAY;gDAAE,SAAS;4CAAE;4CACzB,WAAU;;8DAEV,8OAAC,oMAAM,CAAC,CAAC;oDACP,MAAM,QAAQ,OAAO;oDACrB,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;8DAEV,cAAA,8OAAC,sOAAY;wDAAC,MAAM;;;;;;;;;;;8DAEtB,8OAAC,oMAAM,CAAC,CAAC;oDACP,MAAM,QAAQ,SAAS;oDACvB,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;8DAEV,cAAA,8OAAC,gNAAM;wDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;8CAMpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAItB,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,0BAC/B,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;sDASX,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAM,QAAQ,OAAO;oDACrB,WAAU;;sEAEV,8OAAC;sEAAK;;;;;;sEACN,8OAAC,gOAAU;4DAAC,MAAM;;;;;;;;;;;;8DAEpB,8OAAC;oDACC,MAAM,QAAQ,SAAS;oDACvB,WAAU;;sEAEV,8OAAC,gNAAM;4DAAC,MAAM;;;;;;sEACd,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;2BAxEP;;;;;;;;;;8BAiFX,8OAAC,oMAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,SAAS,SAAS,YAAY;oBAC9B,WAAU;8BAET,SAAS,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,QAAQ,EAAE,GAAG,CAAC,CAAC,SAAS,sBAC3D,8OAAC,oMAAM,CAAC,GAAG;4BAET,UAAU;4BACV,YAAY;gCAAE,GAAG,CAAC;gCAAG,OAAO;4BAAK;4BACjC,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAwE;;;;;;;;;;;8CAMzF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA8B,QAAQ,KAAK;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAItB,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,0BAC3C,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;sDASX,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAM,QAAQ,OAAO;oDACrB,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,MAAM,QAAQ,SAAS;oDACvB,WAAU;8DAEV,cAAA,8OAAC,gNAAM;wDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;2BA3Cf;;;;;;;;;;8BAoDX,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,oMAAM,CAAC,MAAM;wBACZ,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 1835, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/Potfolio/src/components/Contact.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { useState } from 'react';\nimport { Mail, Phone, MapPin, Github, Linkedin, Twitter, Send } from 'lucide-react';\n\nconst Contact = () => {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    message: '',\n  });\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate form submission\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    // Reset form\n    setFormData({ name: '', email: '', message: '' });\n    setIsSubmitting(false);\n    \n    // You would typically send the data to your backend here\n    alert('Thank you for your message! I\\'ll get back to you soon.');\n  };\n\n  const contactInfo = [\n    {\n      icon: Mail,\n      label: 'Email',\n      value: '<EMAIL>',\n      href: 'mailto:<EMAIL>',\n    },\n    {\n      icon: Phone,\n      label: 'Phone',\n      value: '+****************',\n      href: 'tel:+15551234567',\n    },\n    {\n      icon: MapPin,\n      label: 'Location',\n      value: 'Your City, Country',\n      href: '#',\n    },\n  ];\n\n  const socialLinks = [\n    {\n      icon: Github,\n      label: 'GitHub',\n      href: 'https://github.com/yourusername',\n    },\n    {\n      icon: Linkedin,\n      label: 'LinkedIn',\n      href: 'https://linkedin.com/in/yourusername',\n    },\n    {\n      icon: Twitter,\n      label: 'Twitter',\n      href: 'https://twitter.com/yourusername',\n    },\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.3,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.8,\n        ease: \"easeOut\",\n      },\n    },\n  };\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-black\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl sm:text-5xl font-bold mb-4\">Get In Touch</h2>\n          <div className=\"w-20 h-1 bg-white rounded-full mx-auto mb-6\" />\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Have a project in mind or just want to chat? I'd love to hear from you.\n          </p>\n        </motion.div>\n\n        <motion.div\n          ref={ref}\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={inView ? \"visible\" : \"hidden\"}\n          className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\"\n        >\n          {/* Contact Information */}\n          <motion.div variants={itemVariants} className=\"space-y-8\">\n            <div>\n              <h3 className=\"text-2xl font-semibold mb-6\">Let's Connect</h3>\n              <p className=\"text-gray-300 leading-relaxed mb-8\">\n                I'm always open to discussing new opportunities, creative projects, \n                or just having a friendly conversation about technology and design.\n              </p>\n            </div>\n\n            {/* Contact Details */}\n            <div className=\"space-y-4\">\n              {contactInfo.map((info, index) => (\n                <motion.a\n                  key={index}\n                  href={info.href}\n                  whileHover={{ x: 10 }}\n                  className=\"flex items-center space-x-4 p-4 bg-gray-900 rounded-lg hover:bg-gray-800 transition-all duration-300 group\"\n                >\n                  <div className=\"p-3 bg-white/10 rounded-full group-hover:bg-white/20 transition-colors\">\n                    <info.icon size={20} className=\"text-white\" />\n                  </div>\n                  <div>\n                    <p className=\"text-gray-400 text-sm\">{info.label}</p>\n                    <p className=\"text-white font-medium\">{info.value}</p>\n                  </div>\n                </motion.a>\n              ))}\n            </div>\n\n            {/* Social Links */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">Follow Me</h4>\n              <div className=\"flex space-x-4\">\n                {socialLinks.map((social, index) => (\n                  <motion.a\n                    key={index}\n                    href={social.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    whileHover={{ scale: 1.1, y: -5 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"p-3 bg-gray-900 rounded-full hover:bg-white hover:text-black transition-all duration-300\"\n                  >\n                    <social.icon size={20} />\n                  </motion.a>\n                ))}\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Contact Form */}\n          <motion.div variants={itemVariants}>\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Name\n                </label>\n                <motion.input\n                  whileFocus={{ scale: 1.02 }}\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleInputChange}\n                  required\n                  className=\"w-full px-4 py-3 bg-gray-900 border border-gray-700 rounded-lg focus:border-white focus:outline-none transition-colors text-white placeholder-gray-500\"\n                  placeholder=\"Your Name\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Email\n                </label>\n                <motion.input\n                  whileFocus={{ scale: 1.02 }}\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  required\n                  className=\"w-full px-4 py-3 bg-gray-900 border border-gray-700 rounded-lg focus:border-white focus:outline-none transition-colors text-white placeholder-gray-500\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Message\n                </label>\n                <motion.textarea\n                  whileFocus={{ scale: 1.02 }}\n                  id=\"message\"\n                  name=\"message\"\n                  value={formData.message}\n                  onChange={handleInputChange}\n                  required\n                  rows={6}\n                  className=\"w-full px-4 py-3 bg-gray-900 border border-gray-700 rounded-lg focus:border-white focus:outline-none transition-colors text-white placeholder-gray-500 resize-none\"\n                  placeholder=\"Tell me about your project or just say hello...\"\n                />\n              </div>\n\n              <motion.button\n                type=\"submit\"\n                disabled={isSubmitting}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"w-full px-6 py-3 bg-white text-black font-semibold rounded-lg hover:bg-gray-200 transition-all duration-300 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isSubmitting ? (\n                  <motion.div\n                    animate={{ rotate: 360 }}\n                    transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                    className=\"w-5 h-5 border-2 border-black border-t-transparent rounded-full\"\n                  />\n                ) : (\n                  <>\n                    <Send size={18} />\n                    <span>Send Message</span>\n                  </>\n                )}\n              </motion.button>\n            </form>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAOA,MAAM,UAAU;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,gLAAS,EAAC;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;QACvC,MAAM;QACN,OAAO;QACP,SAAS;IACX;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IAEjD,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,aAAa;QACb,YAAY;YAAE,MAAM;YAAI,OAAO;YAAI,SAAS;QAAG;QAC/C,gBAAgB;QAEhB,yDAAyD;QACzD,MAAM;IACR;IAEA,MAAM,cAAc;QAClB;YACE,MAAM,0MAAI;YACV,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,6MAAK;YACX,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,oNAAM;YACZ,OAAO;YACP,OAAO;YACP,MAAM;QACR;KACD;IAED,MAAM,cAAc;QAClB;YACE,MAAM,gNAAM;YACZ,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,sNAAQ;YACd,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,mNAAO;YACb,OAAO;YACP,MAAM;QACR;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC,oMAAM,CAAC,GAAG;oBACT,KAAK;oBACL,UAAU;oBACV,SAAQ;oBACR,SAAS,SAAS,YAAY;oBAC9B,WAAU;;sCAGV,8OAAC,oMAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAOpD,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,oMAAM,CAAC,CAAC;4CAEP,MAAM,KAAK,IAAI;4CACf,YAAY;gDAAE,GAAG;4CAAG;4CACpB,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,KAAK,IAAI;wDAAC,MAAM;wDAAI,WAAU;;;;;;;;;;;8DAEjC,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAyB,KAAK,KAAK;;;;;;sEAChD,8OAAC;4DAAE,WAAU;sEAA0B,KAAK,KAAK;;;;;;;;;;;;;2CAV9C;;;;;;;;;;8CAiBX,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC,oMAAM,CAAC,CAAC;oDAEP,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,YAAY;wDAAE,OAAO;wDAAK,GAAG,CAAC;oDAAE;oDAChC,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;8DAEV,cAAA,8OAAC,OAAO,IAAI;wDAAC,MAAM;;;;;;mDARd;;;;;;;;;;;;;;;;;;;;;;sCAgBf,8OAAC,oMAAM,CAAC,GAAG;4BAAC,UAAU;sCACpB,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAA+C;;;;;;0DAG/E,8OAAC,oMAAM,CAAC,KAAK;gDACX,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,IAAI;gDACpB,UAAU;gDACV,QAAQ;gDACR,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAA+C;;;;;;0DAGhF,8OAAC,oMAAM,CAAC,KAAK;gDACX,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,QAAQ;gDACR,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAU,WAAU;0DAA+C;;;;;;0DAGlF,8OAAC,oMAAM,CAAC,QAAQ;gDACd,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,OAAO;gDACvB,UAAU;gDACV,QAAQ;gDACR,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC,oMAAM,CAAC,MAAM;wCACZ,MAAK;wCACL,UAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDAET,6BACC,8OAAC,oMAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,QAAQ;4CAAI;4CACvB,YAAY;gDAAE,UAAU;gDAAG,QAAQ;gDAAU,MAAM;4CAAS;4CAC5D,WAAU;;;;;qGAGZ;;8DACE,8OAAC,0MAAI;oDAAC,MAAM;;;;;;8DACZ,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B;uCAEe", "debugId": null}}, {"offset": {"line": 2339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/Potfolio/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Heart, ArrowUp } from 'lucide-react';\n\nconst Footer = () => {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-gradient-to-t from-gray-900 to-black py-12 border-t border-gray-800\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex flex-col items-center space-y-6\">\n          {/* Back to top button */}\n          <motion.button\n            onClick={scrollToTop}\n            whileHover={{ scale: 1.1, y: -5 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"p-3 bg-white/10 rounded-full hover:bg-white/20 transition-all duration-300 group\"\n          >\n            <ArrowUp size={20} className=\"text-white group-hover:text-gray-200\" />\n          </motion.button>\n\n          {/* Logo/Name */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-center\"\n          >\n            <h3 className=\"text-2xl font-bold text-white mb-2\">Your Name</h3>\n            <p className=\"text-gray-400\">Creating beautiful digital experiences</p>\n          </motion.div>\n\n          {/* Navigation Links */}\n          <motion.nav\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.1 }}\n            className=\"flex flex-wrap justify-center gap-6\"\n          >\n            {['Home', 'About', 'Skills', 'Projects', 'Contact'].map((item) => (\n              <motion.button\n                key={item}\n                whileHover={{ scale: 1.05 }}\n                onClick={() => {\n                  const element = document.getElementById(item.toLowerCase());\n                  if (element) {\n                    element.scrollIntoView({ behavior: 'smooth' });\n                  }\n                }}\n                className=\"text-gray-400 hover:text-white transition-colors duration-300\"\n              >\n                {item}\n              </motion.button>\n            ))}\n          </motion.nav>\n\n          {/* Divider */}\n          <div className=\"w-full max-w-md h-px bg-gradient-to-r from-transparent via-gray-600 to-transparent\" />\n\n          {/* Copyright */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            className=\"text-center text-gray-400 text-sm\"\n          >\n            <p className=\"flex items-center justify-center space-x-1\">\n              <span>© {currentYear} Your Name. Made with</span>\n              <motion.span\n                animate={{ scale: [1, 1.2, 1] }}\n                transition={{ duration: 1, repeat: Infinity }}\n              >\n                <Heart size={16} className=\"text-red-500 fill-current\" />\n              </motion.span>\n              <span>and lots of coffee.</span>\n            </p>\n          </motion.div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,oMAAM,CAAC,MAAM;wBACZ,SAAS;wBACT,YAAY;4BAAE,OAAO;4BAAK,GAAG,CAAC;wBAAE;wBAChC,UAAU;4BAAE,OAAO;wBAAK;wBACxB,WAAU;kCAEV,cAAA,8OAAC,uNAAO;4BAAC,MAAM;4BAAI,WAAU;;;;;;;;;;;kCAI/B,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAI/B,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAET;4BAAC;4BAAQ;4BAAS;4BAAU;4BAAY;yBAAU,CAAC,GAAG,CAAC,CAAC,qBACvD,8OAAC,oMAAM,CAAC,MAAM;gCAEZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,SAAS;oCACP,MAAM,UAAU,SAAS,cAAc,CAAC,KAAK,WAAW;oCACxD,IAAI,SAAS;wCACX,QAAQ,cAAc,CAAC;4CAAE,UAAU;wCAAS;oCAC9C;gCACF;gCACA,WAAU;0CAET;+BAVI;;;;;;;;;;kCAgBX,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC;4BAAE,WAAU;;8CACX,8OAAC;;wCAAK;wCAAG;wCAAY;;;;;;;8CACrB,8OAAC,oMAAM,CAAC,IAAI;oCACV,SAAS;wCAAE,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;oCAAC;oCAC9B,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;8CAE5C,cAAA,8OAAC,6MAAK;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;8CAE7B,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;uCAEe", "debugId": null}}]}